// src/components/ProfessionalsPage/ProfessionalsPage.tsx
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Layout } from '@/components/Layout';
import { ProviderCard } from '@/components/ui/provider-card';
import { Filter, X, ChevronDown, Crown, Search, Loader2, MapPin } from 'lucide-react';
import useEmblaCarousel from 'embla-carousel-react';
import { Button } from '@/components/ui/button';
import { SEO } from '@/components/SEO';
import { useIsMobile } from '@/hooks/use-mobile';
import { getMockProviders, PaginationData } from '@/data/mockProviders';
import { TextRotate } from '@/components/ui/text-rotate';
import { motion } from 'framer-motion';
import { TrustAuthority } from '@/components/TrustAuthority';
import { Pagination } from '@/components/ui/pagination';
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { ProfileDialog } from '@/components/ProfileDialog';
import { Progress } from "@/components/ui/progress";
import { useDebounce, useDebounceValue } from '@/hooks/use-debounce';
import { Badge } from '@/components/ui/badge';
import { EnhancedFilterSidebar } from '@/components/EnhancedFilterSidebar';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { DataType } from "@/types/common.ts";
import { useGeolocation } from "@/hooks/use-geolocation.ts";
import { MobileAppHeader } from '@/components/MobileAppHeader';
import { MobileServiceCategories } from '@/components/MobileServiceCategories';
import { MobileProviderCard } from '@/components/MobileProviderCard';
import { LocationMap } from '@/components/LocationMap';
import { InteractiveProviderMap } from '@/components/InteractiveProviderMap';
import { OptimizedProviderCard } from '@/components/OptimizedProviderCard';

// Constants
const ITEMS_PER_PAGE = 15;
const DEFAULT_ACTIVE_FILTERS = {
  searchQuery: '',
  zipCode: '',
  distance: 25,
  minRating: 0,
  availableNow: false,
  verifiedOnly: false,
  selectedServiceId: null
};
const DEFAULT_ROTATING_WORDS = ["Top-Rated", "Certified", "Professional", "Experienced", "Licensed", "Trusted", "Local"];
const DEFAULT_GRADIENT_BACKGROUND = "linear-gradient(to bottom, #fafafa 0%, #ffffff 100%)";

// Types
export interface ProfessionalsPageProps {
  serviceId: string;
  pageTitle: string;
  pageDescription: string;
  serviceName: string;
  rotatingWords?: string[];
  gradientBackground?: string;
}
interface CarouselProvidersProps {
  providers: DataType[];
  onViewProfile: (provider: DataType) => void;
}
interface ActiveFilters {
  searchQuery: string;
  zipCode: string;
  distance: number;
  minRating: number;
  availableNow: boolean;
  verifiedOnly: boolean;
  selectedServiceId: string | null;
}

// Utility functions
const calculateAverageRating = (reviews: Array<{
  rating: string;
}>): number => {
  if (!reviews || reviews.length === 0) return 0;
  const sum = reviews.reduce((total, review) => total + parseInt(review.rating), 0);
  return parseFloat((sum / reviews.length).toFixed(1));
};

// CarouselProviders component
const CarouselProviders: React.FC<CarouselProvidersProps> = ({
  providers,
  onViewProfile
}) => {
  const [emblaRef] = useEmblaCarousel({
    align: 'start',
    containScroll: 'trimSnaps',
    dragFree: true
  });
  return <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex gap-3">
          {providers.map(provider => <div key={provider.businessId} className="flex-shrink-0 w-[230px]">
                <ProviderCard provider={provider} onViewProfile={() => onViewProfile(provider)} className="h-full" />
              </div>)}
        </div>
      </div>;
};

// Main component
export const ProfessionalsPage: React.FC<ProfessionalsPageProps> = ({
  serviceId,
  pageTitle,
  pageDescription,
  serviceName,
  rotatingWords = DEFAULT_ROTATING_WORDS,
  gradientBackground = DEFAULT_GRADIENT_BACKGROUND
}) => {
  // Hooks
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const contentRef = useRef<HTMLDivElement>(null);
  const {
    loading
  } = useGeolocation();
  // Extract URL params
  const initialPage = parseInt(searchParams.get('page') || '1');
  const initialSearch = searchParams.get('search') || '';
  const initialZipCode = searchParams.get('zip') || '';

  // State
  const [currentPage, setCurrentPage] = useState(initialPage > 0 ? initialPage : 1);
  const [providers, setProviders] = useState<DataType[]>([]);
  const [filteredProviders, setFilteredProviders] = useState<DataType[]>([]);
  const [loadedProviders, setLoadedProviders] = useState<DataType[]>([]);
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<DataType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [sortOption, setSortOption] = useState('recommended');
  const [localSearchQuery, setLocalSearchQuery] = useState(initialSearch);
  const [pagination, setPagination] = useState<PaginationData>({
    current_page: initialPage > 0 ? initialPage : 1,
    per_page: ITEMS_PER_PAGE,
    total: 0,
    last_page: 1
  });
  // State for mobile sections
  const [topRatedProviders, setTopRatedProviders] = useState<DataType[]>([]);
  const [fastestResponseProviders, setFastestResponseProviders] = useState<DataType[]>([]);
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({
    ...DEFAULT_ACTIVE_FILTERS,
    searchQuery: initialSearch,
    zipCode: initialZipCode,
    selectedServiceId: serviceId
  });
  const [loadingTopRatedProviders, setLoadingTopRatedProviders] = useState(true);
  const [loadingFastestResponseProviders, setLoadingFastestResponseProviders] = useState(true);
  const [showMapExpanded, setShowMapExpanded] = useState(false);

  // Derived state
  const debouncedSearchQuery = useDebounceValue(localSearchQuery, 300);
  const [isSearching, setIsSearching] = useState(false);
  const totalPages = useMemo(() => pagination.last_page, [pagination.last_page]);

  // Transform provider data - now using optimized service
  const convertData = useCallback(async (providers: DataType[], serviceId: string) => {
    const { convertProviderData } = await import('@/services/dataProcessingService');
    return convertProviderData(providers, serviceId);
  }, []);

  // Data fetching
  const fetchData = useCallback(async (page = 1, isLoadMore = false) => {
    if (isLoadMore) {
      setIsLoadingMore(true);
    } else {
      setIsLoading(true);
    }
    try {
      const hasSearch = !!activeFilters.searchQuery;
      const baseOptions = {
        page,
        zip_code: activeFilters.zipCode
      };
      const response = await getMockProviders(serviceId, baseOptions);
      const newProviders = await convertData(response.data || [], serviceId);
      if (isLoadMore) {
        // Append new providers for "Load more" functionality
        setLoadedProviders(prev => [...prev, ...newProviders]);
        setProviders(prev => [...prev, ...newProviders]);
        setFilteredProviders(prev => [...prev, ...newProviders]);
      } else {
        setLoadedProviders(newProviders);
        setProviders(newProviders);
        setFilteredProviders(newProviders);
      }
      setPagination(response.pagination);
      if (currentPage !== response.pagination.current_page && !isLoadMore) {
        setCurrentPage(response.pagination.current_page);
        setSearchParams(prev => {
          const newParams = new URLSearchParams(prev);
          newParams.set('page', response.pagination.current_page.toString());
          if (hasSearch) newParams.set('search', activeFilters.searchQuery);
          return newParams;
        });
      }
    } catch (error) {
      if (!isLoadMore) {
        setProviders([]);
        setFilteredProviders([]);
        setLoadedProviders([]);
      }
    } finally {
      if (isLoadMore) {
        setIsLoadingMore(false);
      } else {
        setIsLoading(false);
      }
    }
  }, [serviceId, currentPage, setSearchParams, activeFilters.searchQuery, activeFilters.zipCode, convertData]);

  // Fetch data for Top-Rated Pros section (page 2, 25 items)
  const fetchTopRatedProviders = useCallback(async () => {
    setLoadingTopRatedProviders(true);
    try {
      const options = {
        page: 2,
        per_page: 25,
        zip_code: activeFilters.zipCode
      };
      const response = await getMockProviders(serviceId, options);
      const newProviders = await convertData(response.data || [], serviceId);
      setTopRatedProviders(newProviders);
      setLoadingTopRatedProviders(false);
    } catch (error) {
      console.error("Error fetching top rated providers:", error);
      setTopRatedProviders([]);
      setLoadingTopRatedProviders(false);
    }
  }, [serviceId, activeFilters.zipCode, convertData]);

  // Fetch data for Fastest Response Pros section (page 4, 25 items)
  const fetchFastestResponseProviders = useCallback(async () => {
    setLoadingFastestResponseProviders(true);
    try {
      const options = {
        page: 3,
        per_page: 25,
        zip_code: activeFilters.zipCode
      };
      const response = await getMockProviders(serviceId, options);
      const newProviders = await convertData(response.data || [], serviceId);
      setFastestResponseProviders(newProviders);
      setLoadingFastestResponseProviders(false);
    } catch (error) {
      console.error("Error fetching fastest response providers:", error);
      setFastestResponseProviders([]);
      setLoadingFastestResponseProviders(false);
    }
  }, [serviceId, activeFilters.zipCode, convertData]);

  // Filtering function - now using optimized service
  const debouncedFilterFunction = useCallback(async (filterValues: ActiveFilters) => {
    if (providers.length === 0) return;
    if (!isLoading) setIsLoading(true);
    
    const { filterProviders } = await import('@/services/dataProcessingService');
    const filtered = filterProviders(providers, {
      searchQuery: filterValues.searchQuery,
      distance: filterValues.distance,
      minRating: filterValues.minRating,
      availableNow: filterValues.availableNow,
      verifiedOnly: filterValues.verifiedOnly
    });

    // Reset to page 1 and update URL
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      if (filterValues.searchQuery) {
        newParams.set('search', filterValues.searchQuery);
      } else {
        newParams.delete('search');
      }
      return newParams;
    });

    // Update filtered providers
    setFilteredProviders(filtered);
    setIsLoading(false);
  }, [providers, isLoading, setSearchParams]);

  // Debounced filtering
  const debouncedFilter = useDebounce(debouncedFilterFunction, 300);

  // Event handlers
  const handleFilterChange = useCallback((filterValues: Partial<ActiveFilters>) => {
    if ('searchQuery' in filterValues) {
      setLocalSearchQuery(filterValues.searchQuery || '');

      // Remove searchQuery from filterValues to prevent double processing
      const {
        searchQuery,
        ...otherFilters
      } = filterValues;

      // Only update other filters immediately
      if (Object.keys(otherFilters).length > 0) {
        setActiveFilters(prev => ({
          ...prev,
          ...otherFilters
        }));

        // Update URL params for zipCode changes
        if ('zipCode' in otherFilters) {
          setSearchParams(prev => {
            const newParams = new URLSearchParams(prev);
            if (otherFilters.zipCode) {
              newParams.set('zip', otherFilters.zipCode);
            } else {
              newParams.delete('zip');
            }
            return newParams;
          });
        }
        debouncedFilter({
          ...activeFilters,
          ...otherFilters
        });
      }
    } else {
      setActiveFilters(prev => ({
        ...prev,
        ...filterValues
      }));

      // Update URL params for zipCode changes
      if ('zipCode' in filterValues) {
        setSearchParams(prev => {
          const newParams = new URLSearchParams(prev);
          if (filterValues.zipCode) {
            newParams.set('zip', filterValues.zipCode);
          } else {
            newParams.delete('zip');
          }
          return newParams;
        });
      }
      debouncedFilter({
        ...activeFilters,
        ...filterValues
      });
    }
  }, [debouncedFilter, activeFilters, setSearchParams]);
  const handleOpenProfile = useCallback((provider: DataType) => {
    setSelectedProvider(provider);
    setIsProfileOpen(true);
  }, []);
  const handleCloseProfile = useCallback(() => {
    setSelectedProvider(null);
    setIsProfileOpen(false);
  }, []);
  const handleCloseFilterDrawer = useCallback(() => {
    setFilterDrawerOpen(false);
  }, []);
  const handleSortChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSortOption = e.target.value;
    setSortOption(newSortOption);

    // Reset to page 1 and update URL
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      if (activeFilters.searchQuery) {
        newParams.set('search', activeFilters.searchQuery);
      }
      return newParams;
    });
  }, [setSearchParams, activeFilters.searchQuery]);
  const handleServiceChange = useCallback((newServiceId: string) => {
    // Navigate to the new service page
    if (newServiceId !== serviceId) {
      navigate(`/professionals/${newServiceId}`);
    }
  }, [navigate, serviceId]);
  const clearFilter = useCallback((filterKey: string) => {
    if (filterKey === 'all') {
      // Clear all filters
      setLocalSearchQuery('');
      const resetFilters = {
        ...DEFAULT_ACTIVE_FILTERS,
        selectedServiceId: serviceId
      };
      setActiveFilters(resetFilters);
      debouncedFilter(resetFilters);
    } else {
      // Clear specific filter
      const updatedFilters = {
        ...activeFilters
      };
      if (filterKey === 'searchQuery') {
        setLocalSearchQuery('');
        updatedFilters.searchQuery = '';
      }
      if (filterKey === 'zipCode') updatedFilters.zipCode = '';
      if (filterKey === 'distance') updatedFilters.distance = 25;
      if (filterKey === 'minRating') updatedFilters.minRating = 0;
      if (filterKey === 'availableNow') updatedFilters.availableNow = false;
      if (filterKey === 'verifiedOnly') updatedFilters.verifiedOnly = false;
      setActiveFilters(updatedFilters);
      debouncedFilter(updatedFilters);
    }
  }, [activeFilters, debouncedFilter, serviceId]);
  
  // Handle map location change
  const handleMapLocationChange = useCallback(async (lat: number, lng: number, zipCode?: string) => {
    if (zipCode) {
      handleFilterChange({ zipCode });
    }
  }, [handleFilterChange]);

  // Update search state when debounced query changes
  useEffect(() => {
    setIsSearching(localSearchQuery !== debouncedSearchQuery);
    if (debouncedSearchQuery !== activeFilters.searchQuery) {
      setActiveFilters(prev => ({
        ...prev,
        searchQuery: debouncedSearchQuery
      }));
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearchQuery) {
          newParams.set('search', debouncedSearchQuery);
        } else {
          newParams.delete('search');
        }
        return newParams;
      });
    }
  }, [debouncedSearchQuery, localSearchQuery, activeFilters.searchQuery, setSearchParams]);
  const fetchAllProviders = useCallback(async () => {
    try {
      await Promise.all([fetchTopRatedProviders(), fetchFastestResponseProviders()]);
    } catch (error) {
      console.error("Error fetching providers:", error);
    }
  }, [fetchTopRatedProviders, fetchFastestResponseProviders]);

  // Fetch data when zipCode changes
  useEffect(() => {
    const debounceTimer = setTimeout(async () => {
      if (!loading) {
        await fetchData(currentPage);
        if (isMobile && activeFilters.zipCode.length) {
          await fetchAllProviders();
        }
      }
    }, activeFilters.zipCode.length ? 1000 : 0);
    return () => clearTimeout(debounceTimer);
  }, [activeFilters.zipCode, currentPage, fetchData, isMobile, fetchAllProviders, loading]);

  // Scroll to top when page changes
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }, [currentPage]);

  // Handle case where current page is greater than total pages
  useEffect(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      setCurrentPage(totalPages);
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('page', totalPages.toString());
        if (activeFilters.searchQuery) {
          newParams.set('search', activeFilters.searchQuery);
        }
        return newParams;
      });
    }
  }, [totalPages, currentPage, setSearchParams, activeFilters.searchQuery]);

  // Memoized values
  const paginatedProviders = useMemo(() => {
    return isMobile ? loadedProviders : filteredProviders;
  }, [filteredProviders, loadedProviders, isMobile]);
  const hasActiveFilters = useMemo(() => {
    return localSearchQuery !== '' || activeFilters.zipCode !== '' || activeFilters.distance < 25 || activeFilters.minRating > 0 || activeFilters.availableNow || activeFilters.verifiedOnly;
  }, [localSearchQuery, activeFilters]);

  // Render
  return <Layout hideNav={isMobile} className="bg-gray-50">
        <SEO 
          title={pageTitle} 
          description={pageDescription} 
          localBusinessSchema={true} 
          serviceType={serviceId} 
          serviceSlug={serviceId} 
          canonicalUrl={`/professionals/${serviceId}`}
          enhancedSchema={true}
          pageType="professionals"
          breadcrumbs={[
            { name: "Home", url: "https://jobon.app" },
            { name: "Professionals", url: "https://jobon.app/professionals" },
            { name: serviceName, url: `https://jobon.app/professionals/${serviceId}` }
          ]}
        />

        {/* Mobile App Header */}
        {isMobile && <MobileAppHeader 
          searchQuery={localSearchQuery} 
          onSearchChange={setLocalSearchQuery} 
          onSearchSubmit={() => {
            setActiveFilters(prev => ({
              ...prev,
              searchQuery: localSearchQuery
            }));
          }}
          zipCode={activeFilters.zipCode}
          onZipCodeChange={(zipCode) => handleFilterChange({ zipCode })}
        />}

        {/* Desktop Hero Section - keep existing for desktop */}
        {!isMobile && <div className="pt-20 pb-8" style={{
      background: gradientBackground
    }}>
            <div className="container mx-auto px-4 md:px-6">
              <div className="md:text-center mb-6 md:mb-10">
                <h1 className="text-3xl md:text-5xl font-bold mb-2 md:mb-4 text-gray-900 dark:text-white">
                  <motion.div className="flex flex-col items-center">
                    <div className="flex items-center justify-center">
                      <span>Find </span>
                      <TextRotate texts={rotatingWords} mainClassName="overflow-hidden text-primary mx-3 dark:text-blue-300" staggerDuration={0.03} staggerFrom="last" rotationInterval={3000} transition={{
                  type: "spring",
                  damping: 30,
                  stiffness: 400
                }} />
                      <span>{serviceName}</span>
                    </div>
                    <span>In Your Area</span>
                  </motion.div>
                </h1>
                <p className="text-base md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                  Browse through our network of trusted {serviceName.toLowerCase()} for all your {serviceId} needs.
                </p>
              </div>
            </div>
          </div>}

        {/* Mobile Service Categories */}
        {isMobile && <MobileServiceCategories selectedServiceId={serviceId} onServiceChange={handleServiceChange} />}

        {/* Mobile Location Map */}
        {isMobile && (
          <div className="bg-gray-50">
            <InteractiveProviderMap 
              height="200px" 
              className="w-full" 
              providers={filteredProviders}
              onLocationChange={handleMapLocationChange}
              isExpanded={showMapExpanded}
              onToggleExpand={() => setShowMapExpanded(!showMapExpanded)}
            />
          </div>
        )}

        {/* Desktop Location Section - keep for desktop */}
        {!isMobile && <div className="mb-4 py-3 bg-slate-100">
            <div className="container mx-auto px-4">
              <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
                <div className="flex items-center text-primary gap-2">
                  <MapPin className="h-5 w-5" />
                  <span className="font-medium">Your Location:</span>
                </div>
                <div className="flex items-center gap-2">
                  <input type="text" placeholder="Enter your ZIP code" value={activeFilters.zipCode} onChange={e => handleFilterChange({
              zipCode: e.target.value
            })} className="px-3 py-2 border rounded-md text-sm w-40" />
                  <Button size="sm" className="whitespace-nowrap">
                    Update Location
                  </Button>
                </div>
              </div>
            </div>
          </div>}

        {/* Mobile Location Input */}
        {isMobile}

        {/* Desktop Filter Sidebar - keep existing for desktop */}
        {!isMobile && <div className="mb-4 py-3 bg-slate-100">
              <EnhancedFilterSidebar currentServiceId={serviceId} onFilterChange={handleFilterChange} onServiceChange={handleServiceChange} initialSearchQuery={initialSearch} zipCode={activeFilters?.zipCode} />
            </div>}

        {/* Main Content */}
        <div className={`${isMobile ? 'px-0' : 'container mx-auto px-0 md:px-6'} mb-8`}>
          <div className="flex flex-col md:flex-row">
            {/* Desktop Sidebar - keep existing */}
            {!isMobile && <div className="w-[260px] flex-shrink-0">
                  <div className="pr-5 border-r border-gray-200 dark:border-gray-700">
                    <EnhancedFilterSidebar currentServiceId={serviceId} onFilterChange={handleFilterChange} onServiceChange={handleServiceChange} initialSearchQuery={initialSearch} zipCode={activeFilters?.zipCode} />
                  </div>
                </div>}

            {/* Main Content Area */}
            <div ref={contentRef} className={`flex-grow max-w-[1089px] ${isMobile ? 'px-0' : 'pt-3 md:pt-0 md:pl-6 px-0'} py-2 mx-0 my-0`}>
              {/* Desktop Results Header - keep for desktop */}
              {!isMobile && <div className="flex justify-between items-center mb-4">
                  <div className="hidden md:block">
                    {pagination.total} results
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="relative hidden md:block">
                      <select className="appearance-none bg-transparent border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1.5 pr-8 text-gray-800 dark:text-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-primary" onChange={handleSortChange} value={sortOption}>
                        <option value="recommended">Recommended</option>
                        <option value="rating">Highest Rated</option>
                      </select>
                      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none" />
                    </div>
                  </div>
                </div>}

              {/* Mobile Results Count */}
              {isMobile && filteredProviders.length > 0 && <div className="mb-4 px-4">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {filteredProviders.length} {serviceName} Near You
                  </h2>
                </div>}

              {/* Loading Indicator */}
              {isLoading && <div className="py-4">
                    <Progress value={65} className="h-1" />
                    <p className="text-sm text-gray-500 mt-2">Loading results...</p>
                  </div>}

              {/* Provider Results */}
              {!isLoading && <div className={isMobile ? "space-y-4 px-4" : "space-y-4"}>
                    {paginatedProviders.length === 0 ? <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
                          <p className="text-gray-500">No providers match your search</p>
                        </div> : <>
                          <div className="min-h-[200px]">
                            {isMobile ?
                // Mobile Provider Cards
                paginatedProviders.map(provider => <MobileProviderCard key={provider.businessId} provider={provider} onViewProfile={handleOpenProfile} onContact={() => {
                  // Handle contact logic
                }} />) :
                // Desktop Provider Cards - now using optimized component
                paginatedProviders.map(provider => <div key={provider.businessId} className={`mb-5 ${provider.isFeatured ? "relative" : ""}`}>
                                      <OptimizedProviderCard provider={provider} onViewProfile={() => handleOpenProfile(provider)} />
                                  </div>)}
                          </div>
                        </>}
                  </div>}

              {/* Mobile Load More Button */}
              {isMobile && pagination.current_page < pagination.last_page && !isLoading && <div className="mt-5 px-4">
                    <Button variant="outline" className="w-full py-3 bg-white border-gray-300 hover:bg-gray-50" onClick={async () => {
              await fetchData(pagination.current_page + 1, true);
            }} disabled={isLoadingMore} type='button'>
                      {isLoadingMore ? <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Loading more...
                          </> : "Load more"}
                    </Button>
                  </div>}

              {/* Desktop Pagination - keep existing */}
              {!isMobile && totalPages > 1 && !isLoading && <div className="mt-8">
                    <Pagination totalItems={pagination.total} itemsPerPage={pagination.per_page} currentPage={pagination.current_page} onPageChange={async page => {
              setCurrentPage(page);
              setSearchParams(prev => {
                const newParams = new URLSearchParams(prev);
                newParams.set('page', page.toString());
                if (activeFilters.searchQuery) {
                  newParams.set('search', activeFilters.searchQuery);
                }
                return newParams;
              });
              await fetchData(page);
            }} className="w-full justify-center" />
                  </div>}

              {/* Trust Section - keep existing */}
              <div className="mt-8">
                <TrustAuthority />
              </div>
            </div>
          </div>
        </div>

        {/* Profile Dialog - keep existing */}
        {selectedProvider && <ProfileDialog isOpen={isProfileOpen} onClose={handleCloseProfile} provider={selectedProvider} />}
      </Layout>;
};
export default ProfessionalsPage;