
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { YoastSEOData } from '@/services/wordpressApi';
import { generateOrganizationSchema } from './schema/OrganizationSchema';
import { generateServiceSchema } from './schema/ServiceSchema';
import { generateWebSiteSchema } from './schema/WebSiteSchema';
import { generateBreadcrumbSchema } from './schema/BreadcrumbSchema';
import { generateReviewAggregateSchema } from './schema/ReviewAggregateSchema';

// Helper function to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  if (!text) return '';

  // First decode
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  let decoded = textarea.value;

  // Check if there are still HTML entities that need decoding (specifically &amp;)
  if (decoded.includes('&amp;')) {
    // Second decode for nested entities
    textarea.innerHTML = decoded;
    decoded = textarea.value;
  }

  return decoded;
};

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface SEOProps {
  title: string;
  description: string;
  schema?: Record<string, any>;
  canonicalUrl?: string;
  image?: string;
  localBusinessSchema?: boolean;
  serviceType?: string;
  serviceSlug?: string;
  yoastSEO?: YoastSEOData;
  // Enhanced schema props
  enhancedSchema?: boolean;
  pageType?: 'homepage' | 'service' | 'about' | 'blog' | 'professionals';
  breadcrumbs?: BreadcrumbItem[];
  serviceOffers?: Array<{
    name: string;
    description: string;
    priceRange?: string;
  }>;
  priceRange?: string;
}

export const SEO: React.FC<SEOProps> = ({ 
  title, 
  description, 
  schema, 
  canonicalUrl,
  image = "https://lovable.dev/opengraph-image-p98pqg.png",
  localBusinessSchema = false,
  serviceType = "",
  serviceSlug = "",
  yoastSEO,
  enhancedSchema = true,
  pageType = 'homepage',
  breadcrumbs = [],
  serviceOffers = [],
  priceRange = "$50-$500"
}) => {
  const siteUrl = "https://jobon.app";

  // Use Yoast SEO data if available, otherwise use custom data
  // Decode HTML entities in titles and descriptions to fix special character issues
  const seoTitle = yoastSEO?.title ? decodeHtmlEntities(yoastSEO.title) : title;
  const seoDescription = yoastSEO?.description ? decodeHtmlEntities(yoastSEO.description) : description;
  const seoCanonical = yoastSEO?.canonical || (canonicalUrl ? `${siteUrl}${canonicalUrl}` : siteUrl);
  const seoImage = yoastSEO?.opengraphImage || image;

  // For OpenGraph and Twitter, prioritize Yoast data
  const ogTitle = yoastSEO?.opengraphTitle ? decodeHtmlEntities(yoastSEO.opengraphTitle) : seoTitle;
  const ogDescription = yoastSEO?.opengraphDescription ? decodeHtmlEntities(yoastSEO.opengraphDescription) : seoDescription;
  const ogType = yoastSEO?.opengraphType || "website";

  const twitterTitle = yoastSEO?.twitterTitle ? decodeHtmlEntities(yoastSEO.twitterTitle) : ogTitle;
  const twitterDescription = yoastSEO?.twitterDescription ? decodeHtmlEntities(yoastSEO.twitterDescription) : ogDescription;
  const twitterImage = yoastSEO?.twitterImage || seoImage;

  // For backward compatibility
  const fullTitle = seoTitle ? `${seoTitle} | JobON` : "JobON | Help around the home at your fingertips";
  const fullUrl = seoCanonical;
  const logoUrl = "https://www.jobon.app/images/logo.png";

  // Generate LocalBusiness schema if needed
  const generateLocalBusinessSchema = () => {
    if (!localBusinessSchema || !serviceType || !serviceSlug) return null;

    return {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "JobON",
      "url": `https://www.jobon.app/services/${serviceSlug}`,
      "image": logoUrl,
      "logo": logoUrl,
      "description": `Compare bids from local ${serviceType} pros. JobON makes hiring fast, affordable, and protected—so the job gets done right, every time.`,
      "areaServed": {
        "@type": "Country",
        "name": "United States"
      },
      "serviceType": serviceType,
      "brand": {
        "@type": "Brand",
        "name": "JobON"
      },
      "hasMap": "https://www.jobon.app/locations",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "1540"
      },
      "sameAs": [
        "https://www.facebook.com/JobONapp",
        "https://www.instagram.com/JobONapp",
        "https://www.linkedin.com/company/jobonapp"
      ]
    };
  };

  const localBusinessSchemaData = generateLocalBusinessSchema();

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={seoDescription} />

      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />

      {/* Robot Meta Tags from Yoast if available */}
      {yoastSEO?.metaRobotsNoindex && (
        <meta name="robots" content={`${yoastSEO.metaRobotsNoindex},${yoastSEO.metaRobotsNofollow}`} />
      )}

      {/* OpenGraph Tags for Social Sharing */}
      <meta property="og:title" content={ogTitle} />
      <meta property="og:description" content={ogDescription} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={ogType} />
      <meta property="og:image" content={seoImage} />

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@jobon_app" />
      <meta name="twitter:title" content={twitterTitle} />
      <meta name="twitter:description" content={twitterDescription} />
      <meta name="twitter:image" content={twitterImage} />

      {/* Schema.org JSON-LD - Prioritize Yoast schema if available */}
      {(yoastSEO?.schema || schema) && (
        <script type="application/ld+json">
          {JSON.stringify(yoastSEO?.schema || schema)}
        </script>
      )}

      {/* LocalBusiness Schema.org JSON-LD if applicable and no Yoast schema */}
      {!yoastSEO?.schema && localBusinessSchemaData && (
        <script type="application/ld+json">
          {JSON.stringify(localBusinessSchemaData)}
        </script>
      )}

      {/* Enhanced Schema Components - Only render if no Yoast schema and enhancedSchema is enabled */}
      {!yoastSEO?.schema && enhancedSchema && (
        <>
          {/* Website schema for homepage */}
          {pageType === 'homepage' && (
            <script type="application/ld+json">
              {JSON.stringify(generateWebSiteSchema())}
            </script>
          )}
          
          {/* Organization schema */}
          <script type="application/ld+json">
            {JSON.stringify(generateOrganizationSchema({
              serviceType: serviceType || "Home and Commercial Services"
            }))}
          </script>
          
          {/* Service-specific schema */}
          {(pageType === 'service' || serviceType) && (
            <script type="application/ld+json">
              {JSON.stringify(generateServiceSchema({
                name: serviceType ? `${serviceType} Services` : title,
                description: description,
                serviceType: serviceType || "Home Services",
                url: canonicalUrl ? `https://jobon.app${canonicalUrl}` : "https://jobon.app",
                offers: serviceOffers,
                priceRange: priceRange
              }))}
            </script>
          )}
          
          {/* Breadcrumb schema */}
          {breadcrumbs.length > 0 && (
            <script type="application/ld+json">
              {JSON.stringify(generateBreadcrumbSchema(breadcrumbs))}
            </script>
          )}
          
          {/* Review aggregate schema for service pages */}
          {pageType === 'service' && (
            <script type="application/ld+json">
              {JSON.stringify(generateReviewAggregateSchema({
                itemName: serviceType ? `${serviceType} Services` : title,
                itemUrl: canonicalUrl ? `https://jobon.app${canonicalUrl}` : "https://jobon.app",
                aggregateRating: {
                  ratingValue: "4.8",
                  reviewCount: "1540"
                }
              }))}
            </script>
          )}
        </>
      )}
    </Helmet>
  );
};
