import React from 'react';

export interface FAQItem {
  question: string;
  answer: string;
}

interface FAQPageSchemaProps {
  faqs: FAQItem[];
  mainEntity?: string;
}

export const generateFAQPageSchema = ({
  faqs,
  mainEntity = "Service Information"
}: FAQPageSchemaProps) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    })),
    "about": {
      "@type": "Thing",
      "name": mainEntity
    }
  };
};

export const FAQPageSchema: React.FC<FAQPageSchemaProps> = (props) => {
  const schema = generateFAQPageSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};