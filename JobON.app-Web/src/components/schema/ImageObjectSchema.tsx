import React from 'react';

interface ImageObjectSchemaProps {
  url: string;
  contentUrl?: string;
  thumbnailUrl?: string;
  caption?: string;
  description?: string;
  width?: number;
  height?: number;
  encodingFormat?: string;
  representativeOfPage?: boolean;
  author?: string;
  copyrightHolder?: string;
  license?: string;
}

export const generateImageObjectSchema = ({
  url,
  contentUrl = url,
  thumbnailUrl,
  caption,
  description,
  width = 800,
  height = 600,
  encodingFormat = "image/jpeg",
  representativeOfPage = false,
  author = "JobON",
  copyrightHolder = "JobON",
  license = "https://jobon.app/license"
}: ImageObjectSchemaProps) => {
  const imageSchema: any = {
    "@context": "https://schema.org",
    "@type": "ImageObject",
    "url": url,
    "contentUrl": contentUrl,
    "width": width,
    "height": height,
    "encodingFormat": encodingFormat,
    "representativeOfPage": representativeOfPage,
    "author": {
      "@type": "Organization",
      "name": author
    },
    "copyrightHolder": {
      "@type": "Organization", 
      "name": copyrightHolder
    },
    "license": license
  };

  if (thumbnailUrl) {
    imageSchema.thumbnailUrl = thumbnailUrl;
  }

  if (caption) {
    imageSchema.caption = caption;
  }

  if (description) {
    imageSchema.description = description;
  }

  return imageSchema;
};

export const ImageObjectSchema: React.FC<ImageObjectSchemaProps> = (props) => {
  const schema = generateImageObjectSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};