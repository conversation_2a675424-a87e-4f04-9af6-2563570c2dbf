import React from 'react';

interface LocalBusinessSchemaProps {
  name?: string;
  description?: string;
  address?: {
    streetAddress?: string;
    addressLocality?: string;
    addressRegion?: string;
    postalCode?: string;
    addressCountry?: string;
  };
  geo?: {
    latitude: number;
    longitude: number;
  };
  telephone?: string;
  email?: string;
  url?: string;
  image?: string;
  priceRange?: string;
  paymentAccepted?: string[];
  currenciesAccepted?: string[];
  openingHours?: string[];
  amenityFeature?: string[];
  serviceArea?: string[];
  hasCredential?: string[];
  knowsAbout?: string[];
}

export const generateLocalBusinessSchema = ({
  name = "JobON Local Services",
  description = "Professional home and business services in your area through verified local service providers.",
  address = {
    addressCountry: "United States"
  },
  geo,
  telephone = "******-JOBON-1",
  email = "<EMAIL>",
  url = "https://jobon.app",
  image = "https://jobon.app/images/local-business.jpg",
  priceRange = "$$",
  paymentAccepted = ["Cash", "Credit Card", "Debit Card", "Mobile Payment"],
  currenciesAccepted = ["USD"],
  openingHours = [
    "Mo-Su 00:00-24:00"
  ],
  amenityFeature = [
    "Free Estimates",
    "Licensed Professionals", 
    "Insurance Coverage",
    "24/7 Customer Support",
    "Satisfaction Guarantee"
  ],
  serviceArea = ["United States"],
  hasCredential = [
    "Licensed and Insured Professionals",
    "Background Checked Service Providers",
    "Verified Business Credentials"
  ],
  knowsAbout = [
    "Home Cleaning Services",
    "Plumbing Repair",
    "Electrical Services", 
    "HVAC Installation",
    "Landscaping",
    "Handyman Services",
    "Painting Services",
    "Roofing Repair"
  ]
}: LocalBusinessSchemaProps = {}) => {
  const businessSchema: any = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": name,
    "description": description,
    "address": {
      "@type": "PostalAddress",
      ...address
    },
    "telephone": telephone,
    "email": email,
    "url": url,
    "image": image,
    "priceRange": priceRange,
    "paymentAccepted": paymentAccepted,
    "currenciesAccepted": currenciesAccepted,
    "openingHours": openingHours,
    "amenityFeature": amenityFeature.map(feature => ({
      "@type": "LocationFeatureSpecification",
      "name": feature
    })),
    "areaServed": serviceArea.map(area => ({
      "@type": "Country",
      "name": area
    })),
    "hasCredential": hasCredential.map(credential => ({
      "@type": "EducationalOccupationalCredential",
      "name": credential
    })),
    "knowsAbout": knowsAbout,
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1540",
      "bestRating": "5",
      "worstRating": "1"
    }
  };

  if (geo) {
    businessSchema.geo = {
      "@type": "GeoCoordinates",
      "latitude": geo.latitude,
      "longitude": geo.longitude
    };
  }

  return businessSchema;
};

export const LocalBusinessSchema: React.FC<LocalBusinessSchemaProps> = (props) => {
  const schema = generateLocalBusinessSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};