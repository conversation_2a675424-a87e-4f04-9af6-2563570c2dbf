import React from 'react';

interface OfferSchemaProps {
  name: string;
  description: string;
  price?: number;
  priceCurrency?: string;
  priceRange?: string;
  availability?: string;
  validFrom?: string;
  validThrough?: string;
  eligibleRegion?: string;
  seller?: string;
  category?: string;
  itemOffered?: {
    name: string;
    description: string;
    type: string;
  };
}

export const generateOfferSchema = ({
  name,
  description,
  price,
  priceCurrency = "USD",
  priceRange = "$50-$500",
  availability = "https://schema.org/InStock",
  validFrom = new Date().toISOString().split('T')[0],
  validThrough = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  eligibleRegion = "United States",
  seller = "JobON",
  category = "Home Services",
  itemOffered
}: OfferSchemaProps) => {
  const offerSchema: any = {
    "@context": "https://schema.org",
    "@type": "Offer",
    "name": name,
    "description": description,
    "priceCurrency": priceCurrency,
    "availability": availability,
    "validFrom": validFrom,
    "validThrough": validThrough,
    "eligibleRegion": {
      "@type": "Country",
      "name": eligibleRegion
    },
    "seller": {
      "@type": "Organization",
      "name": seller,
      "url": "https://jobon.app"
    },
    "category": category,
    "businessFunction": "http://purl.org/goodrelations/v1#ProvideService"
  };

  if (price) {
    offerSchema.price = price;
  } else {
    offerSchema.priceRange = priceRange;
  }

  if (itemOffered) {
    offerSchema.itemOffered = {
      "@type": itemOffered.type,
      "name": itemOffered.name,
      "description": itemOffered.description
    };
  }

  return offerSchema;
};

export const OfferSchema: React.FC<OfferSchemaProps> = (props) => {
  const schema = generateOfferSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};