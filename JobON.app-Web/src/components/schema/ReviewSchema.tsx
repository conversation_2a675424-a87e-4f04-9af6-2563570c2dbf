import React from 'react';

export interface ReviewAuthor {
  name: string;
  url?: string;
  image?: string;
}

interface ReviewSchemaProps {
  itemReviewed: {
    name: string;
    type: string;
  };
  author: ReviewAuthor;
  reviewRating: {
    ratingValue: number;
    bestRating?: number;
    worstRating?: number;
  };
  reviewBody: string;
  datePublished: string;
  publisher?: string;
  url?: string;
}

export const generateReviewSchema = ({
  itemReviewed,
  author,
  reviewRating,
  reviewBody,
  datePublished,
  publisher = "JobON",
  url = "https://jobon.app"
}: ReviewSchemaProps) => {
  return {
    "@context": "https://schema.org",
    "@type": "Review",
    "itemReviewed": {
      "@type": itemReviewed.type,
      "name": itemReviewed.name
    },
    "author": {
      "@type": "Person",
      "name": author.name,
      "url": author.url,
      "image": author.image
    },
    "reviewRating": {
      "@type": "Rating",
      "ratingValue": reviewRating.ratingValue,
      "bestRating": reviewRating.bestRating || 5,
      "worstRating": reviewRating.worstRating || 1
    },
    "reviewBody": reviewBody,
    "datePublished": datePublished,
    "publisher": {
      "@type": "Organization",
      "name": publisher,
      "url": url
    }
  };
};

export const ReviewSchema: React.FC<ReviewSchemaProps> = (props) => {
  const schema = generateReviewSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};