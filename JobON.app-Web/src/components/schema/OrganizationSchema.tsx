import React from 'react';

interface OrganizationSchemaProps {
  name?: string;
  url?: string;
  logo?: string;
  description?: string;
  foundingDate?: string;
  areaServed?: string;
  serviceType?: string;
}

interface OrganizationSchemaEnhancedProps extends OrganizationSchemaProps {
  departments?: Array<{
    name: string;
    description: string;
    services: string[];
  }>;
  hasCredential?: string[];
  knowsAbout?: string[];
  makesOffer?: Array<{
    name: string;
    description: string;
    category: string;
  }>;
  numberOfEmployees?: string;
  slogan?: string;
  awards?: string[];
}

export const generateOrganizationSchema = ({
  name = "JobON",
  url = "https://jobon.app",
  logo = "https://www.jobon.app/images/logo.png",
  description = "JobON connects homeowners and businesses with trusted local service professionals. Compare bids, hire verified pros, and get your projects done right.",
  foundingDate = "2024",
  areaServed = "United States",
  serviceType = "Home and Commercial Services",
  departments = [
    {
      name: "Cleaning Services",
      description: "Professional cleaning services for homes and businesses",
      services: ["House Cleaning", "Office Cleaning", "Deep Cleaning", "Move-in/Move-out Cleaning"]
    },
    {
      name: "Home Repair Services",
      description: "Expert repair and maintenance services",
      services: ["Plumbing", "Electrical", "HVAC", "Handyman Services"]
    },
    {
      name: "Landscaping Services",
      description: "Complete outdoor maintenance and design",
      services: ["Lawn Care", "Tree Services", "Garden Design", "Irrigation Systems"]
    }
  ],
  hasCredential = [
    "Licensed Service Providers",
    "Insured Professionals",
    "Background Checked Contractors",
    "Certified Business Partners"
  ],
  knowsAbout = [
    "Home Cleaning Services",
    "Plumbing Repair and Installation",
    "Electrical Services and Wiring",
    "HVAC Installation and Repair",
    "Landscaping and Garden Design",
    "Handyman Services",
    "Painting and Decorating",
    "Roofing and Gutter Services",
    "Flooring Installation",
    "Kitchen and Bathroom Remodeling"
  ],
  makesOffer = [
    {
      name: "Professional Service Matching",
      description: "Connect with verified local service professionals",
      category: "Platform Service"
    },
    {
      name: "Quote Comparison",
      description: "Compare multiple quotes from different providers",
      category: "Comparison Service"
    },
    {
      name: "Quality Assurance",
      description: "Satisfaction guarantee on all completed projects",
      category: "Service Guarantee"
    }
  ],
  numberOfEmployees = "50-100",
  slogan = "Your Trusted Local Service Connection",
  awards = [
    "Best Home Services Platform 2024",
    "Customer Choice Award",
    "Innovation in Service Technology"
  ]
}: OrganizationSchemaEnhancedProps = {}) => {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": name,
    "url": url,
    "logo": {
      "@type": "ImageObject",
      "url": logo,
      "width": 200,
      "height": 60
    },
    "description": description,
    "foundingDate": foundingDate,
    "numberOfEmployees": numberOfEmployees,
    "slogan": slogan,
    "areaServed": {
      "@type": "Country",
      "name": areaServed
    },
    "serviceType": serviceType,
    "department": departments.map(dept => ({
      "@type": "Organization",
      "name": dept.name,
      "description": dept.description,
      "parentOrganization": {
        "@type": "Organization",
        "name": name
      }
    })),
    "hasCredential": hasCredential.map(credential => ({
      "@type": "EducationalOccupationalCredential",
      "name": credential
    })),
    "knowsAbout": knowsAbout,
    "makesOffer": makesOffer.map(offer => ({
      "@type": "Offer",
      "name": offer.name,
      "description": offer.description,
      "category": offer.category,
      "seller": {
        "@type": "Organization",
        "name": name
      }
    })),
    "award": awards,
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": `${url}/contact`,
      "availableLanguage": ["English", "Spanish"],
      "hoursAvailable": {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
        "opens": "06:00",
        "closes": "22:00"
      }
    },
    "sameAs": [
      "https://www.facebook.com/JobONapp",
      "https://www.instagram.com/JobONapp", 
      "https://www.linkedin.com/company/jobonapp",
      "https://twitter.com/jobon_app"
    ],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1540",
      "bestRating": "5",
      "worstRating": "1"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Home and Business Services",
      "itemListElement": departments.map(dept => ({
        "@type": "OfferCatalog",
        "name": dept.name,
        "description": dept.description,
        "itemListElement": dept.services.map(service => ({
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": service,
            "category": dept.name
          }
        }))
      }))
    }
  };
};

// Legacy component for backward compatibility
export const OrganizationSchema: React.FC<OrganizationSchemaProps> = (props) => {
  const schema = generateOrganizationSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};