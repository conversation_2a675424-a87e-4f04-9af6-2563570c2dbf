import React from 'react';

interface WebSiteSchemaProps {
  name?: string;
  url?: string;
  description?: string;
  searchUrl?: string;
}

export const generateWebSiteSchema = ({
  name = "JobON",
  url = "https://jobon.app",
  description = "Find trusted local service professionals for home and business needs. Compare bids and hire the best pros for your projects.",
  searchUrl = "https://jobon.app/professionals?search={search_term_string}"
}: WebSiteSchemaProps = {}) => {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": name,
    "url": url,
    "description": description,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": searchUrl
      },
      "query-input": {
        "@type": "PropertyValueSpecification",
        "valueRequired": true,
        "valueName": "search_term_string"
      }
    },
    "publisher": {
      "@type": "Organization", 
      "name": name,
      "url": url,
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.jobon.app/images/logo.png"
      }
    },
    "inLanguage": "en-US",
    "copyrightYear": "2024",
    "copyrightHolder": {
      "@type": "Organization",
      "name": name
    }
  };
};

// Legacy component for backward compatibility
export const WebSiteSchema: React.FC<WebSiteSchemaProps> = (props) => {
  const schema = generateWebSiteSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};