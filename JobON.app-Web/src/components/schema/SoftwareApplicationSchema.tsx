import React from 'react';

interface SoftwareApplicationSchemaProps {
  name?: string;
  description?: string;
  url?: string;
  applicationCategory?: string;
  operatingSystem?: string[];
  softwareVersion?: string;
  datePublished?: string;
  author?: string;
  downloadUrl?: string;
  fileSize?: string;
  requirements?: string;
  softwareHelp?: string;
  releaseNotes?: string;
  screenshot?: string[];
  aggregateRating?: {
    ratingValue: string;
    reviewCount: string;
  };
  offers?: {
    price: string;
    priceCurrency: string;
  };
}

export const generateSoftwareApplicationSchema = ({
  name = "JobON App",
  description = "Connect with trusted local service professionals. Get quotes, compare prices, and hire verified pros for home and business services.",
  url = "https://jobon.app",
  applicationCategory = "BusinessApplication",
  operatingSystem = ["Web Browser", "iOS", "Android"],
  softwareVersion = "1.0.0",
  datePublished = "2024-01-01",
  author = "JobON",
  downloadUrl = "https://jobon.app",
  fileSize = "2MB",
  requirements = "Internet connection, modern web browser",
  softwareHelp = "https://jobon.app/help",
  releaseNotes = "https://jobon.app/releases",
  screenshot = [
    "https://jobon.app/images/app-screenshot-1.jpg",
    "https://jobon.app/images/app-screenshot-2.jpg"
  ],
  aggregateRating = {
    ratingValue: "4.8",
    reviewCount: "1540"
  },
  offers = {
    price: "0",
    priceCurrency: "USD"
  }
}: SoftwareApplicationSchemaProps = {}) => {
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": name,
    "description": description,
    "url": url,
    "applicationCategory": applicationCategory,
    "operatingSystem": operatingSystem,
    "softwareVersion": softwareVersion,
    "datePublished": datePublished,
    "author": {
      "@type": "Organization",
      "name": author,
      "url": "https://jobon.app"
    },
    "downloadUrl": downloadUrl,
    "fileSize": fileSize,
    "requirements": requirements,
    "softwareHelp": {
      "@type": "CreativeWork",
      "url": softwareHelp
    },
    "releaseNotes": releaseNotes,
    "screenshot": screenshot.map(url => ({
      "@type": "ImageObject",
      "url": url
    })),
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": aggregateRating.ratingValue,
      "reviewCount": aggregateRating.reviewCount,
      "bestRating": "5",
      "worstRating": "1"
    },
    "offers": {
      "@type": "Offer",
      "price": offers.price,
      "priceCurrency": offers.priceCurrency,
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "Find Local Service Professionals",
      "Compare Multiple Quotes",
      "Verified Professional Profiles",
      "Secure Payment Processing",
      "Customer Review System",
      "Project Management Tools",
      "Real-time Communication"
    ]
  };
};

export const SoftwareApplicationSchema: React.FC<SoftwareApplicationSchemaProps> = (props) => {
  const schema = generateSoftwareApplicationSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};