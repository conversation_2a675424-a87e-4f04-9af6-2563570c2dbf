import React from 'react';

interface ArticleSchemaProps {
  headline: string;
  description: string;
  author: {
    name: string;
    url?: string;
  };
  datePublished: string;
  dateModified?: string;
  publisher?: string;
  image?: string;
  url?: string;
  wordCount?: number;
  articleSection?: string;
  speakable?: {
    type: string;
    cssSelector: string[];
  };
  mainEntityOfPage?: string;
}

export const generateArticleSchema = ({
  headline,
  description,
  author,
  datePublished,
  dateModified = datePublished,
  publisher = "JobON",
  image = "https://jobon.app/images/article-default.jpg",
  url = "https://jobon.app",
  wordCount,
  articleSection = "Home Services",
  speakable = {
    type: "SpeakableSpecification",
    cssSelector: ["h1", "h2", ".article-summary"]
  },
  mainEntityOfPage = url
}: ArticleSchemaProps) => {
  const articleSchema: any = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": headline,
    "description": description,
    "author": {
      "@type": "Person",
      "name": author.name,
      "url": author.url
    },
    "datePublished": datePublished,
    "dateModified": dateModified,
    "publisher": {
      "@type": "Organization",
      "name": publisher,
      "url": "https://jobon.app",
      "logo": {
        "@type": "ImageObject",
        "url": "https://jobon.app/images/logo.png"
      }
    },
    "image": {
      "@type": "ImageObject",
      "url": image
    },
    "url": url,
    "articleSection": articleSection,
    "speakable": {
      "@type": speakable.type,
      "cssSelector": speakable.cssSelector
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": mainEntityOfPage
    }
  };

  if (wordCount) {
    articleSchema.wordCount = wordCount;
  }

  return articleSchema;
};

export const ArticleSchema: React.FC<ArticleSchemaProps> = (props) => {
  const schema = generateArticleSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};