// Schema component exports for easier importing
export { OrganizationSchema, generateOrganizationSchema } from './OrganizationSchema';
export { ServiceSchema, generateServiceSchema } from './ServiceSchema';
export { WebSiteSchema, generateWebSiteSchema } from './WebSiteSchema';
export { BreadcrumbSchema, generateBreadcrumbSchema } from './BreadcrumbSchema';
export { ReviewAggregateSchema, generateReviewAggregateSchema } from './ReviewAggregateSchema';
export { FAQPageSchema, generateFAQPageSchema } from './FAQPageSchema';
export { HowToSchema, generateHowToSchema } from './HowToSchema';
export { JobPostingSchema, generateJobPostingSchema } from './JobPostingSchema';
export { ReviewSchema, generateReviewSchema } from './ReviewSchema';
export { OfferSchema, generateOfferSchema } from './OfferSchema';
export { ArticleSchema, generateArticleSchema } from './ArticleSchema';
export { ImageObjectSchema, generateImageObjectSchema } from './ImageObjectSchema';
export { LocalBusinessSchema, generateLocalBusinessSchema } from './LocalBusinessSchema';
export { SoftwareApplicationSchema, generateSoftwareApplicationSchema } from './SoftwareApplicationSchema';

// Export types for easier importing
export type { FAQItem } from './FAQPageSchema';
export type { HowToStep } from './HowToSchema';
export type { ReviewAuthor } from './ReviewSchema';