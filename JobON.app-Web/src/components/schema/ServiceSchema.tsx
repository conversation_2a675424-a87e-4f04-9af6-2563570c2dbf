import React from 'react';

interface ServiceSchemaProps {
  name: string;
  description: string;
  provider?: string;
  serviceType: string;
  areaServed?: string;
  url?: string;
  image?: string;
  priceRange?: string;
  aggregateRating?: {
    ratingValue: string;
    reviewCount: string;
  };
  offers?: Array<{
    name: string;
    description: string;
    priceRange?: string;
  }>;
}

interface ServiceSchemaEnhancedProps extends ServiceSchemaProps {
  serviceAudience?: string[];
  serviceOutput?: string;
  additionalProperty?: Array<{
    name: string;
    value: string;
  }>;
  serviceArea?: string[];
  hasCredential?: string[];
  department?: string;
  duration?: string;
  availableChannel?: string[];
}

export const generateServiceSchema = ({
  name,
  description,
  provider = "JobON",
  serviceType,
  areaServed = "United States", 
  url = "https://jobon.app",
  image = "https://jobon.app/images/service-hero.jpg",
  priceRange = "$50-$500",
  aggregateRating = {
    ratingValue: "4.8",
    reviewCount: "1540"
  },
  offers = [],
  serviceAudience = ["Homeowners", "Business Owners", "Property Managers"],
  serviceOutput = "Professional service completion with satisfaction guarantee",
  additionalProperty = [
    { name: "Licensed", value: "Yes" },
    { name: "Insured", value: "Yes" },
    { name: "Background Checked", value: "Yes" }
  ],
  serviceArea = ["United States", "Urban Areas", "Suburban Areas"],
  hasCredential = ["Licensed Professional", "Insured Service Provider"],
  department = "Home Services",
  duration = "PT1H-PT8H",
  availableChannel = ["Online", "Mobile App", "Phone"]
}: ServiceSchemaEnhancedProps) => {
  return {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": name,
    "description": description,
    "provider": {
      "@type": "Organization",
      "name": provider,
      "url": url,
      "department": {
        "@type": "Organization",
        "name": department
      }
    },
    "serviceType": serviceType,
    "areaServed": serviceArea.map(area => ({
      "@type": "Place",
      "name": area
    })),
    "serviceArea": serviceArea.map(area => ({
      "@type": "Place", 
      "name": area
    })),
    "serviceAudience": serviceAudience.map(audience => ({
      "@type": "Audience",
      "audienceType": audience
    })),
    "serviceOutput": serviceOutput,
    "additionalProperty": additionalProperty.map(prop => ({
      "@type": "PropertyValue",
      "name": prop.name,
      "value": prop.value
    })),
    "hasCredential": hasCredential.map(credential => ({
      "@type": "EducationalOccupationalCredential",
      "name": credential
    })),
    "url": url,
    "image": image,
    "priceRange": priceRange,
    "duration": duration,
    "availableChannel": availableChannel.map(channel => ({
      "@type": "ServiceChannel",
      "name": channel
    })),
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": aggregateRating.ratingValue,
      "reviewCount": aggregateRating.reviewCount,
      "bestRating": "5",
      "worstRating": "1"
    },
    "offers": offers.map(offer => ({
      "@type": "Offer",
      "name": offer.name,
      "description": offer.description,
      "priceRange": offer.priceRange || priceRange,
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock",
      "validFrom": new Date().toISOString().split('T')[0],
      "businessFunction": "http://purl.org/goodrelations/v1#ProvideService",
      "seller": {
        "@type": "Organization",
        "name": provider
      }
    })),
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": `${name} Services`,
      "itemListElement": offers.map((offer, index) => ({
        "@type": "Offer",
        "position": index + 1,
        "itemOffered": {
          "@type": "Service",
          "name": offer.name,
          "description": offer.description,
          "category": serviceType
        }
      }))
    },
    "category": serviceType,
    "audience": {
      "@type": "Audience",
      "audienceType": serviceAudience.join(", ")
    },
    "isRelatedTo": {
      "@type": "Service",
      "name": "Professional Home Services",
      "category": "Home Improvement"
    }
  };
};

// Legacy component for backward compatibility
export const ServiceSchema: React.FC<ServiceSchemaProps> = (props) => {
  const schema = generateServiceSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};