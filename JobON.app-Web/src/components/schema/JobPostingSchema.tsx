import React from 'react';

interface JobPostingSchemaProps {
  title: string;
  description: string;
  hiringOrganization?: string;
  jobLocation?: string;
  employmentType?: string;
  baseSalary?: {
    minValue: number;
    maxValue: number;
    unitText: string;
  };
  datePosted?: string;
  validThrough?: string;
  qualifications?: string[];
  responsibilities?: string[];
  skills?: string[];
  benefits?: string[];
}

export const generateJobPostingSchema = ({
  title,
  description,
  hiringOrganization = "JobON",
  jobLocation = "United States",
  employmentType = "CONTRACTOR",
  baseSalary = {
    minValue: 25,
    maxValue: 100,
    unitText: "HOUR"
  },
  datePosted = new Date().toISOString().split('T')[0],
  validThrough = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  qualifications = [],
  responsibilities = [],
  skills = [],
  benefits = []
}: JobPostingSchemaProps) => {
  return {
    "@context": "https://schema.org",
    "@type": "JobPosting",
    "title": title,
    "description": description,
    "hiringOrganization": {
      "@type": "Organization",
      "name": hiringOrganization,
      "sameAs": "https://jobon.app"
    },
    "jobLocation": {
      "@type": "Place",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": jobLocation
      }
    },
    "employmentType": employmentType,
    "baseSalary": {
      "@type": "MonetaryAmount",
      "currency": "USD",
      "value": {
        "@type": "QuantitativeValue",
        "minValue": baseSalary.minValue,
        "maxValue": baseSalary.maxValue,
        "unitText": baseSalary.unitText
      }
    },
    "datePosted": datePosted,
    "validThrough": validThrough,
    "qualifications": qualifications.join(", "),
    "responsibilities": responsibilities.join(", "),
    "skills": skills.join(", "),
    "jobBenefits": benefits.join(", "),
    "industry": "Home Services",
    "occupationalCategory": "Service Provider"
  };
};

export const JobPostingSchema: React.FC<JobPostingSchemaProps> = (props) => {
  const schema = generateJobPostingSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};