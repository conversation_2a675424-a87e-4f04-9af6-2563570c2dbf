import React from 'react';

interface Review {
  author: string;
  rating: string;
  reviewBody: string;
  datePublished: string;
}

interface ReviewAggregateSchemaProps {
  itemName: string;
  itemUrl?: string;
  aggregateRating: {
    ratingValue: string;
    reviewCount: string;
    bestRating?: string;
    worstRating?: string;
  };
  reviews?: Review[];
}

export const generateReviewAggregateSchema = ({
  itemName,
  itemUrl = "https://jobon.app",
  aggregateRating,
  reviews = []
}: ReviewAggregateSchemaProps) => {
  return {
    "@context": "https://schema.org",
    "@type": "Product", // or "Service" depending on use case
    "name": itemName,
    "url": itemUrl,
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": aggregateRating.ratingValue,
      "reviewCount": aggregateRating.reviewCount,
      "bestRating": aggregateRating.bestRating || "5",
      "worstRating": aggregateRating.worstRating || "1"
    },
    "review": reviews.map(review => ({
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": review.author
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": review.rating,
        "bestRating": "5",
        "worstRating": "1"
      },
      "reviewBody": review.reviewBody,
      "datePublished": review.datePublished
    }))
  };
};

// Legacy component for backward compatibility
export const ReviewAggregateSchema: React.FC<ReviewAggregateSchemaProps> = (props) => {
  const schema = generateReviewAggregateSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};