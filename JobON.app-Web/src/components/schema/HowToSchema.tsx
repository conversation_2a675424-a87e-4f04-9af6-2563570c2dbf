import React from 'react';

export interface HowToStep {
  name: string;
  text: string;
  image?: string;
  url?: string;
}

interface HowToSchemaProps {
  name: string;
  description: string;
  steps: HowToStep[];
  totalTime?: string;
  estimatedCost?: string;
  supply?: string[];
  tool?: string[];
  image?: string;
}

export const generateHowToSchema = ({
  name,
  description,
  steps,
  totalTime = "PT1H",
  estimatedCost = "$50-$200",
  supply = [],
  tool = [],
  image = "https://jobon.app/images/how-to-guide.jpg"
}: HowToSchemaProps) => {
  return {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": name,
    "description": description,
    "image": image,
    "totalTime": totalTime,
    "estimatedCost": {
      "@type": "MonetaryAmount",
      "currency": "USD",
      "value": estimatedCost
    },
    "supply": supply.map(item => ({
      "@type": "HowToSupply",
      "name": item
    })),
    "tool": tool.map(item => ({
      "@type": "HowToTool", 
      "name": item
    })),
    "step": steps.map((step, index) => ({
      "@type": "HowToStep",
      "position": index + 1,
      "name": step.name,
      "text": step.text,
      "image": step.image,
      "url": step.url
    }))
  };
};

export const HowToSchema: React.FC<HowToSchemaProps> = (props) => {
  const schema = generateHowToSchema(props);
  return (
    <script type="application/ld+json">
      {JSON.stringify(schema)}
    </script>
  );
};