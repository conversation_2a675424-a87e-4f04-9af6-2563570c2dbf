<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('provider_verifications', function (Blueprint $table) {
            $table->string('business_license_number')->nullable()->after('document_type');
            $table->string('tax_id')->nullable()->after('business_license_number');
            $table->string('business_registration_state', 2)->nullable()->after('tax_id');
            $table->timestamp('submitted_at')->nullable()->after('business_registration_state');
            $table->json('documents')->nullable()->after('submitted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('provider_verifications', function (Blueprint $table) {
            $table->dropColumn([
                'business_license_number',
                'tax_id',
                'business_registration_state',
                'submitted_at',
                'documents'
            ]);
        });
    }
};
