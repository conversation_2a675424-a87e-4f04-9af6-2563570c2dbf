<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Company fields
            $table->string('company_name')->nullable()->after('experience_duration');
            $table->string('company_email')->nullable()->after('company_name');
            $table->bigInteger('company_phone')->nullable()->after('company_email');
            $table->bigInteger('company_code')->nullable()->after('company_phone');
            
            // Service and location fields
            $table->string('service_address')->nullable()->after('location_cordinates');
            $table->integer('service_radius')->nullable()->after('service_address');
            $table->string('selected_availability')->nullable()->after('service_radius');
            
            // Professional fields
            $table->string('license_number')->nullable()->after('selected_availability');
            $table->text('work_description')->nullable()->after('license_number');
            
            // Business fields
            $table->string('business_license_number')->nullable()->after('work_description');
            $table->string('tax_id')->nullable()->after('business_license_number');
            $table->string('business_registration_state', 2)->nullable()->after('tax_id');
            
            // Document fields
            $table->string('insurance_certificate')->nullable()->after('business_registration_state');
            $table->json('professional_certifications')->nullable()->after('insurance_certificate');
            
            // Additional business info
            $table->string('company_website')->nullable()->after('professional_certifications');
            $table->string('years_in_business')->nullable()->after('company_website');
            $table->text('about_me')->nullable()->after('years_in_business');
            
            // Preferences
            $table->boolean('auto_bidding')->default(false)->after('about_me');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'company_name',
                'company_email',
                'company_phone',
                'company_code',
                'service_address',
                'service_radius',
                'selected_availability',
                'license_number',
                'work_description',
                'business_license_number',
                'tax_id',
                'business_registration_state',
                'insurance_certificate',
                'professional_certifications',
                'company_website',
                'years_in_business',
                'about_me',
                'auto_bidding'
            ]);
        });
    }
};
